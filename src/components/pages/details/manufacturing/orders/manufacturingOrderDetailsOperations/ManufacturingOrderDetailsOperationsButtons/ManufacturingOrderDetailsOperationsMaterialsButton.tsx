import {FC, useCallback, useEffect, useState} from 'react';

import {useFieldArray, useFormContext, useWatch} from 'react-hook-form';

import AddItemModal from '@/components/ui/special/AddItemModal/AddItemModal';
import useItemActions from '@/hooks/useItemActions';
import useItemStockActions from '@/hooks/useItemStockActions';
import useManufacturingOrderActions from '@/hooks/useManufacturingOrderActions';
import {ManufacturingOrder, ManufacturingOrderMaterial} from '@/types/manufacturing';

type Props = {
  index: number;
  onClose: () => void;
};

const ManufacturingOrderDetailsOperationsMaterialsButton: FC<Props> = ({index, onClose}) => {
  const {getItem} = useItemActions();
  const {control, setValue, watch} = useFormContext<ManufacturingOrder>();
  const {append} = useFieldArray({control, name: `manufacturingOperations.${index}.materials`});
  const {getItemStocks} = useItemStockActions();
  const {processMaterials} = useManufacturingOrderActions();
  const [materialsAdded, setMaterialsAdded] = useState(false);

  useEffect(() => {
    if (materialsAdded) {
      setValue('materials', processMaterials(watch()));
      onClose();
    }
  }, [materialsAdded, onClose, processMaterials, setValue, watch]);

  const handleAdd = useCallback(
    async (values: {id: string; quantity: number}[]) => {
      const entries = await Promise.all(
        values.map(async ({id, quantity}) => {
          const item = await getItem(id);
          if (!item) return null;

          const stocks = await getItemStocks(item.id, {
            id: watch('id'),
            type: 'manufacturing',
          });

          return {
            allAvailable: stocks >= watch('quantity'),
            cost: {
              amount: item.inventoryCostPerItem.amount * watch('quantity'),
              currency: item.inventoryCostPerItem.currency,
            },
            materialGoods: [
              {
                ...item,
                available: stocks >= watch('quantity'),
                cost: item.inventoryCostPerItem,
                lastOrderedFrom: null,
                measurementUnit: item.measurementUnit,
              },
            ],
            onStock: stocks,
            required: quantity,
            requiredDimensions: null,
            requiredTotal: quantity * watch('quantity'),
            reservedTotal: quantity * watch('quantity'),
            totalRequiredDimensions: null,
            wastePercentage: 0,
          };
        }),
      );

      const validEntries = entries.filter(Boolean);

      append(validEntries as ManufacturingOrderMaterial[]);
      setValue('materials', processMaterials(watch()));
      setMaterialsAdded(true);
    },
    [append, getItem, getItemStocks, processMaterials, setValue, watch],
  );

  return <AddItemModal create='full' onAdd={handleAdd} onClose={onClose} />;
};

export default ManufacturingOrderDetailsOperationsMaterialsButton;
