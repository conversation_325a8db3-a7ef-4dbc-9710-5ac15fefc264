import {FC, useCallback, useEffect, useState} from 'react';

import Jo<PERSON> from 'joi';
import {CheckIcon, ChevronRightIcon, Trash2Icon, XIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {Checkbox, ControlledCheckbox} from '@/components/ui/Checkbox';
import {PopoverInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import InventoryItemLink from '@/components/ui/special/InventoryItemLink/InventoryItemLink';
import {ItemHistoryModal} from '@/components/ui/special/ItemHistoryModal';
import {OrderButton} from '@/components/ui/special/OrderButton';
import SetDimensionPopover from '@/components/ui/special/SetDimensionPopover';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useItemActions from '@/hooks/useItemActions';
import {InventoryItem, InventoryItemRequiredMaterial} from '@/types/inventory';
import {ManufacturingOrderMaterial} from '@/types/manufacturing';
import {classes} from '@/utils/common';
import {formatCurrency, formatNumber} from '@/utils/format';
import useItemStockActions from 'hooks/useItemStockActions';
import useSettings from 'hooks/useSettings';

import InventoryItemsDetailsMaterialsTableRowConfigButton from './InventoryItemsDetailsMaterialsTableRowConfig/InventoryItemsDetailsMaterialsTableRowConfigButton';
import InventoryItemsDetailsMaterialsTableSubRow from './InventoryItemsDetailsMaterialsTableSubRow';

type Props = {
  embedded?: boolean;
  excludeIds: string[];
  index: number;
  operationIndex?: number;
  requiredMaterial: InventoryItemRequiredMaterial;
  saveItem: () => void;
};

const InventoryItemsDetailsMaterialsTableRow: FC<Props> = ({
  embedded,
  excludeIds,
  index,
  operationIndex,
  requiredMaterial,
  saveItem,
}) => {
  const t = useTranslations();
  const primaryOption = requiredMaterial.options?.[0];
  const [open, setOpen] = useState(false);
  const {control, setValue, watch} = useFormContext<InventoryItem>();
  const {remove, update} = useFieldArray({control, name: `manufacturingOperations.${operationIndex || 0}.materials`});
  const {getItemAvailable} = useItemStockActions();
  const {hasPermission, isLoading} = useHasPermission();
  const {settings} = useSettings();
  const {processMaterials} = useItemActions();

  const updateMaterial = useCallback(
    (material: Partial<InventoryItemRequiredMaterial>) => {
      update(index, {
        ...requiredMaterial,
        ...material,
      });
      setTimeout(() => {
        setValue('requiredMaterials', processMaterials(watch()));
      }, 200);
    },
    [index, processMaterials, requiredMaterial, setValue, update, watch],
  );

  if (!primaryOption || isLoading) return null;

  const options = requiredMaterial.options.slice(1);

  return (
    <>
      <TableRow className='h-[61px]' key={primaryOption.id}>
        <TableCell className='inline-flex w-full items-center justify-between gap-2'>
          <div className='inline-flex items-start gap-2'>
            <InventoryItemLink item={primaryOption} />
          </div>
          {primaryOption.produced && (
            <Button onClick={() => setOpen((prev) => !prev)} variant='ghost'>
              <ChevronRightIcon className={classes('transition-transform', open && '-rotate-90')} />
            </Button>
          )}
        </TableCell>
        {embedded && (
          <>
            <TableCell>
              <InventoryItemsDetailsMaterialsTableRowConfigButton
                categoryId={requiredMaterial.category?.id}
                excludeIds={excludeIds}
                index={index}
                operationIndex={operationIndex}
                options={options}
                primaryOption={primaryOption}
                requiredMaterial={requiredMaterial}
                saveItem={saveItem}
              />
            </TableCell>
            <TableCell>
              <WithLabel direction='horizontal'>
                <Checkbox
                  checked={requiredMaterial.optional}
                  disabled={!embedded}
                  onCheckedChange={(value) => updateMaterial({optional: !!value})}
                />
              </WithLabel>
            </TableCell>
          </>
        )}
        <TableCell className='text-right'>
          <div className='flex items-center justify-end gap-1'>
            {embedded && primaryOption.material && (
              <SetDimensionPopover
                itemId={primaryOption.id}
                onChange={(value, dimensions) => updateMaterial({quantity: value, requiredDimensions: dimensions})}
                shape={primaryOption.material.shape.shape}
                value={watch(`requiredMaterials.${index}.requiredDimensions`)}
              />
            )}
            <PopoverInput
              className='w-fit'
              defaultValue={requiredMaterial.quantity.toString()}
              disabled={!embedded}
              label={t('quantity')}
              onChange={async (value) => {
                const available = await getItemAvailable(primaryOption.id, Number(value), {
                  inventoryUnitIds: [
                    settings.general.inventoryAccountingSettings.unitDesignations?.defaultInventoryUnit,
                  ],
                });

                updateMaterial({
                  options: [
                    {
                      ...primaryOption,
                      available,
                      cost: {
                        ...primaryOption?.cost,
                        amount: Number(
                          ((primaryOption?.cost?.amount / requiredMaterial.quantity) * Number(value)).toFixed(2),
                        ),
                      },
                    },
                    ...options.splice(1),
                  ],
                  quantity: Number(value),
                });
              }}
              validation={Joi.number().required().positive()}
            >
              {requiredMaterial.quantity} {t(`unit.name.${primaryOption.measurementUnit?.name || 'pcs'}` as any)}
            </PopoverInput>
          </div>
        </TableCell>
        <TableCell className='text-right'>
          <PopoverInput
            className='w-fit justify-end'
            defaultValue={requiredMaterial.wastePercentage.toString()}
            disabled={!embedded}
            label={`${t('waste')} (%)`}
            onChange={(value) => {
              updateMaterial({
                wastePercentage: Number(value),
              });
            }}
            validation={Joi.number().required().positive().allow(0)}
          >
            {formatNumber(requiredMaterial.wastePercentage)}%
          </PopoverInput>
        </TableCell>
        {hasPermission('financial', 'inventory') && (
          <TableCell className='text-right'>{formatCurrency(primaryOption.cost)}</TableCell>
        )}
        <TableCell className='text-right'>
          <ItemHistoryModal item={primaryOption}>
            <Badge className='cursor-pointer gap-1' variant={primaryOption.available ? 'success' : 'error'}>
              {primaryOption.available && <CheckIcon className='size-4 text-green' />}
              {!primaryOption.available && <XIcon className='size-4 text-red' />}
              {t(primaryOption.available ? 'available' : 'unavailable', {isPlural: 'true'})}
            </Badge>
          </ItemHistoryModal>
        </TableCell>
        <TableActions>
          <OrderButton forTable item={primaryOption} />
          {embedded && (
            <Button
              onClick={() => {
                remove(index);
                setTimeout(() => {
                  setValue('requiredMaterials', processMaterials(watch()));
                }, 200);
              }}
              size='icon'
              variant='none'
            >
              <Trash2Icon className='size-5 text-red' strokeWidth={1} />
            </Button>
          )}
        </TableActions>
      </TableRow>
      {open && <InventoryItemsDetailsMaterialsTableSubRow embedded={embedded} id={primaryOption.id} />}
    </>
  );
};

export default InventoryItemsDetailsMaterialsTableRow;
