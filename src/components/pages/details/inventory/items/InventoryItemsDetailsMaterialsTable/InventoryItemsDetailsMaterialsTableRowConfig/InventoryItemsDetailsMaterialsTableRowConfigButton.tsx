import {FC, useState} from 'react';

import {PencilIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {Checkbox} from '@/components/ui/Checkbox';
import {Label, WithLabel} from '@/components/ui/Label';
import {Sheet, SheetClose, Sheet<PERSON>ontent, SheetFooter, SheetPage, SheetTitle, SheetTrigger} from '@/components/ui/Sheet';
import {Dot} from '@/components/ui/special/Dot';
import {Table, TableBody, TableHead, TableHeadActions, TableHeader, TableRow} from '@/components/ui/Table';
import useItemActions from '@/hooks/useItemActions';
import {InventoryItem, InventoryItemOption, InventoryItemRequiredMaterial} from '@/types/inventory';

import InventoryItemsDetailsMaterialsTableRowConfigRows from './InventoryItemsDetailsMaterialsTableRowConfigRows';

type Props = {
  categoryId: string | undefined;
  excludeIds: string[];
  index: number;
  operationIndex?: number;
  options: InventoryItemOption[];
  primaryOption: InventoryItemOption;
  requiredMaterial: InventoryItemRequiredMaterial;
  saveItem: () => void;
};

const InventoryItemsDetailsMaterialsTableRowConfigButton: FC<Props> = ({
  categoryId,
  excludeIds,
  index,
  operationIndex,
  options,
  primaryOption,
  requiredMaterial,
  saveItem,
}) => {
  const t = useTranslations();
  const [config, setConfig] = useState({
    configurableWithOptions: requiredMaterial.configurableWithOptions,
    options: options,
    replaceableWithOptions: requiredMaterial.replaceableWithOptions,
  });
  const {setValue} = useFormContext<InventoryItem>();
  const {getItem} = useItemActions();

  return (
    <Sheet
      onOpenChange={(open) => {
        if (open)
          setConfig({
            configurableWithOptions:
              !requiredMaterial.configurableWithOptions && !requiredMaterial.replaceableWithOptions
                ? true
                : requiredMaterial.configurableWithOptions,
            options: options,
            replaceableWithOptions: requiredMaterial.replaceableWithOptions,
          });
      }}
    >
      <div className='inline-flex gap-1'>
        {(options.length === 0 ||
          (!requiredMaterial.replaceableWithOptions && !requiredMaterial.configurableWithOptions)) && (
          <SheetTrigger asChild>
            <Button size='none' variant='none'>
              <PencilIcon className='size-5' />
            </Button>
          </SheetTrigger>
        )}
        {(requiredMaterial.replaceableWithOptions || requiredMaterial.configurableWithOptions) && (
          <>
            {options.slice(0, 2).map((option) => (
              <SheetTrigger asChild key={option.id}>
                <Badge className='cursor-pointer text-xs'>
                  <Dot variant={requiredMaterial.configurableWithOptions ? 'orange' : 'purple'}>{option.name}</Dot>
                </Badge>
              </SheetTrigger>
            ))}
            {options.length > 2 && (
              <SheetTrigger asChild>
                <Badge className='cursor-pointer text-xs'>
                  <Dot variant={requiredMaterial.configurableWithOptions ? 'orange' : 'purple'}>
                    +{options.length - 2}
                  </Dot>
                </Badge>
              </SheetTrigger>
            )}
          </>
        )}
      </div>
      <SheetPage side='right' size='ms'>
        <SheetTitle className='text-2xl'>
          {primaryOption.name} ({primaryOption.code})
        </SheetTitle>
        <div className='inline-flex items-center justify-center gap-4'>
          <WithLabel direction='horizontal'>
            <Checkbox
              checked={config.configurableWithOptions}
              id='configurable'
              onCheckedChange={(val) => {
                setConfig((prev) => ({
                  ...prev,
                  configurableWithOptions: !!val,
                  replaceableWithOptions: !!val ? false : config.replaceableWithOptions,
                }));
              }}
            />
            <Label htmlFor='configurable'>{t('configurable')}</Label>
          </WithLabel>
          <WithLabel direction='horizontal'>
            <Checkbox
              checked={config.replaceableWithOptions}
              id='replaceable'
              onCheckedChange={(val) => {
                setConfig((prev) => ({
                  ...prev,
                  configurableWithOptions: !!val ? false : config.configurableWithOptions,
                  replaceableWithOptions: !!val,
                }));
              }}
            />
            <Label htmlFor='replaceable'>{t('alternative')}</Label>
          </WithLabel>
        </div>
        <SheetContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('name')}</TableHead>
                <TableHeadActions />
              </TableRow>
            </TableHeader>
            <TableBody className='overflow-y-hidden' isValidating={false}>
              <InventoryItemsDetailsMaterialsTableRowConfigRows
                categoryId={categoryId}
                disabled={!config.configurableWithOptions && !config.replaceableWithOptions}
                excludeIds={excludeIds}
                onChange={async (opts) => {
                  const resolvedItems = await Promise.all(opts.map(async (opt) => await getItem(opt.id)));

                  const newOptions = resolvedItems
                    .filter((item) => item !== undefined)
                    .map((item) => ({
                      ...item,
                      available: true,
                      cost: item.sellPrice,
                      lastOrderedFrom: item.lastPurchase
                        ? {id: item.lastPurchase.supplierId, name: item.lastPurchase.supplierName}
                        : null,
                    }));

                  setConfig((prev) => ({...prev, options: newOptions}));
                }}
                options={config.options}
                primaryOption={primaryOption}
              />
            </TableBody>
          </Table>
        </SheetContent>
        <SheetFooter className='px-6'>
          <SheetClose asChild>
            <Button
              onClick={() => {
                setValue(`manufacturingOperations.${operationIndex || 0}.materials.${index}.options`, [
                  primaryOption,
                  ...config.options,
                ]);
                setValue(
                  `manufacturingOperations.${operationIndex || 0}.materials.${index}.configurableWithOptions`,
                  config.configurableWithOptions,
                );
                setValue(
                  `manufacturingOperations.${operationIndex || 0}.materials.${index}.replaceableWithOptions`,
                  config.replaceableWithOptions,
                );
                setValue(
                  `manufacturingOperations.${operationIndex || 0}.materials.${index}.optional`,
                  config.options.length > 0 ? false : requiredMaterial.optional,
                );
                saveItem();
              }}
            >
              {t('save')}
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetPage>
    </Sheet>
  );
};

export default InventoryItemsDetailsMaterialsTableRowConfigButton;
