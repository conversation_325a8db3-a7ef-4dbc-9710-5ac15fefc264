import {FC, useState} from 'react';

import Jo<PERSON> from 'joi';
import {ArrowLeftIcon, EllipsisVerticalIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import {InputLabel, PopoverInput} from '@/components/ui/Input';
import {WithLabel} from '@/components/ui/Label';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {OrderButton} from '@/components/ui/special/OrderButton';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import {useRouter} from '@/hooks/helpers/useRouter';
import useItem from '@/hooks/useItem';

import InventoryItemsDetailsData from './InventoryItemsDetailsData/InventoryItemsDetailsData';
import InventoryItemsDetailsTables from './InventoryItemsDetailsTables';

type Props = {
  id?: string;
};

const InventoryItemsDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    cloneItem,
    deleteItem,
    deleteItemFile,
    isDirty,
    isLoading,
    saveItem,
    uploadItemFile,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      setValue,
      watch,
      ...restUseFormActions
    },
  } = useItem(id);
  const {back} = useRouter();

  if (isLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('name') || t('new item')} - ${t('inventory')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back(`/inventory/items`)} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <PopoverInput
            className='mr-2'
            defaultValue={watch('name')}
            error={!!errors?.name}
            label={t('name')}
            onChange={(value) => setValue('name', value)}
            validation={Joi.string().required()}
          >
            {watch('name') || t('new item name')}
          </PopoverInput>
        </PageHeaderTitle>
        <WithLabel>
          <PopoverInput
            className='mr-1 h-fit'
            defaultValue={watch('code')}
            error={!!errors?.code}
            onChange={(value) => setValue('code', value)}
            validation={Joi.string().required().max(20)}
          >
            {watch('code') || t('new sku')}
          </PopoverInput>
          <InputLabel>{t('sku')}</InputLabel>
        </WithLabel>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {isDirty && <Button onClick={() => saveItem()}>{t('save')}</Button>}
        {id && (
          <>
            {!isDirty && (
              <OrderButton
                item={{
                  code: watch('code'),
                  id: watch('id'),
                  lastOrderedFrom: watch('lastPurchase')
                    ? {id: watch('lastPurchase.supplierId'), name: watch('lastPurchase.supplierName')}
                    : null,
                  name: watch('name'),
                  produced: watch('produced'),
                }}
              />
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className='px-1.5' variant='ghost'>
                  <EllipsisVerticalIcon />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem disabled={isDirty} onSelect={cloneItem}>
                  {t('clone item')}
                </DropdownMenuItem>
                <DropdownMenuItem disabled={!watch('deletable')} onSelect={deleteItem}>
                  {t('delete')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, setValue, watch, ...restUseFormActions}}
        >
          <InventoryItemsDetailsData />
          <InventoryItemsDetailsTables deleteFile={deleteItemFile} saveItem={saveItem} uploadFile={uploadItemFile} />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default InventoryItemsDetails;
