import {FC, useState} from 'react';

import {useSortable} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import Joi from 'joi';
import {useAtomValue} from 'jotai';
import {sortBy} from 'lodash';
import {ChevronDownIcon, ChevronUpIcon, GripHorizontalIcon, PlusIcon, Trash2Icon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {useFieldArray, useFormContext} from 'react-hook-form';

import {Badge} from '@/components/ui/Badge';
import {Button, IconButton} from '@/components/ui/Button';
import {PopoverDurationInput, PopoverInput} from '@/components/ui/Input';
import {TableMultiSelect} from '@/components/ui/special/TableMultiSelect';
import {Switch} from '@/components/ui/Switch';
import {TableActions, TableCell, TableRow} from '@/components/ui/Table';
import useEmployeeActions from '@/hooks/useEmployeeActions';
import useEmployees from '@/hooks/useEmployees';
import useWorkstationActions from '@/hooks/useWorkstationActions';
import useWorkstations from '@/hooks/useWorkstations';
import {defaultCurrencyAtom} from '@/store/defaults';
import {InventoryItem, InventoryItemManufacturingOperation} from '@/types/inventory';
import {classes, minutesToWorkHoursTimeString} from '@/utils/common';
import {formatCurrency} from '@/utils/format';

// import InventoryItemsDetailsOperationsMaterialsButton from './InventoryItemsDetailsOperationsMaterialsButton';
import InventoryItemsDetailsMaterialsTable from '../InventoryItemsDetailsMaterialsTable/InventoryItemsDetailsMaterialsTable';

type Props = {
  index: number;
  operation: InventoryItemManufacturingOperation;
  saveItem: () => void;
};

const InventoryItemsDetailsOperationsTableRow: FC<Props> = ({index, operation, saveItem}) => {
  const {attributes, isDragging, listeners, setNodeRef, transform, transition} = useSortable({id: operation.id});
  const t = useTranslations();
  const {employees, isLoading: employeesIsLoading} = useEmployees();
  const {createEmployee} = useEmployeeActions();
  const {isLoading: workstationsIsLoading, workstations} = useWorkstations();
  const {createWorkstation} = useWorkstationActions();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const {
    control,
    formState: {errors},
    setValue,
    watch,
  } = useFormContext<InventoryItem>();
  const {remove, update} = useFieldArray({control, name: 'manufacturingOperations'});
  const [open, setOpen] = useState(false);
  const [addMaterials, setAddMaterials] = useState(false);

  if (employeesIsLoading || workstationsIsLoading) return null;

  return (
    <>
      <TableRow
        className={classes(isDragging && 'bg-background shadow-xl hover:bg-background')}
        ref={setNodeRef}
        style={{
          ...(transform ? {transform: CSS.Transform.toString({...transform, scaleX: 1, scaleY: 1})} : {}),
          transition,
        }}
      >
        <TableCell className='inline-flex items-center gap-2'>
          <span {...listeners} {...attributes}>
            <GripHorizontalIcon className='size-5 text-slate-900/40' />
          </span>
          <PopoverInput
            defaultValue={operation.name}
            label={t('name')}
            onChange={(value) => update(index, {...operation, name: value})}
            validation={Joi.string().required()}
          >
            {operation.name}
          </PopoverInput>
        </TableCell>
        <TableCell className='text-right'>
          <PopoverDurationInput
            className='w-fit justify-end'
            error={!!errors.manufacturingOperations?.[index]?.durationInMinutes}
            minutes={operation.durationInMinutes}
            onChange={(value) => update(index, {...operation, durationInMinutes: value})}
          >
            {minutesToWorkHoursTimeString(operation.durationInMinutes)}
          </PopoverDurationInput>
        </TableCell>
        <TableCell className='text-right'>{formatCurrency(operation.costPerHour)}</TableCell>
        <TableCell>
          <div className='flex items-center gap-2'>
            <IconButton icon={<PlusIcon className='size-6' />} onClick={() => setAddMaterials(true)} />
            {(operation.materials || []).length > 0 && (
              <Badge className='cursor-pointer' onClick={() => setOpen(!open)} size='sm' variant='info'>
                {operation.materials?.length} {t('materials')}
                {!open && <ChevronDownIcon className='size-5' />}
                {open && <ChevronUpIcon className='size-5' />}
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={operation.candidateEmployees.map((employee) => employee.id)}
            onValueChange={(value) => {
              setValue(
                `manufacturingOperations.${index}.candidateEmployees`,
                employees.filter((employee) => value.includes(employee.id)),
              );
              setValue(`manufacturingOperations.${index}.operationTemplateId`, null);
            }}
            options={sortBy(
              employees.map((employee) => ({id: employee.id, value: employee.name})),
              'name',
            )}
            renderNotFound={(query) => (
              <Button
                className='w-full justify-start'
                onClick={() => {
                  createEmployee({name: query, position: '-'}).then((employee) => {
                    setValue(`manufacturingOperations.${index}.candidateEmployees`, [
                      ...watch(`manufacturingOperations.${index}.candidateEmployees`, []),
                      employee,
                    ]);
                  });
                }}
                variant='secondary'
              >
                {t('create employee')}
              </Button>
            )}
            searchPlaceholder={t('search employee')}
            side='right'
            summaryLabel={t('selected.male')}
          />
        </TableCell>
        <TableCell>
          <TableMultiSelect
            defaultValue={operation.candidateWorkstations.map((workstation) => workstation.id)}
            onValueChange={(value) => {
              setValue(
                `manufacturingOperations.${index}.candidateWorkstations`,
                workstations.filter((workstation) => value.includes(workstation.id)),
              );
              setValue(`manufacturingOperations.${index}.operationTemplateId`, null);
            }}
            options={sortBy(
              workstations.map((workstation) => ({id: workstation.id, value: workstation.name})),
              'name',
            )}
            renderNotFound={(query) => (
              <Button
                className='w-full justify-start'
                onClick={() => {
                  createWorkstation({costPerHour: {amount: 0, currency: defaultCurrency}, name: query}).then(
                    (workstation) => {
                      setValue(`manufacturingOperations.${index}.candidateWorkstations`, [
                        ...watch(`manufacturingOperations.${index}.candidateWorkstations`, []),
                        workstation,
                      ]);
                    },
                  );
                }}
                variant='secondary'
              >
                {t('create workstation')}
              </Button>
            )}
            searchPlaceholder={t('search workstation')}
            side='left'
            summaryLabel={t('selected.male')}
          />
        </TableCell>
        <TableCell>
          <Switch control={control} controlName={`manufacturingOperations.${index}.parallelizable`} />
        </TableCell>
        <TableActions>
          <Button onClick={() => remove(index)} size='icon' variant='none'>
            <Trash2Icon className='size-5 text-red' strokeWidth={1} />
          </Button>
        </TableActions>
      </TableRow>
      {open && (
        <TableRow className='bg-input/50 border-b-black'>
          <TableCell className='p-0' colSpan={99}>
            <InventoryItemsDetailsMaterialsTable
              embedded
              materials={watch(`manufacturingOperations.${index}.materials`) || []}
              operationIndex={index}
              saveItem={saveItem}
            />
          </TableCell>
        </TableRow>
      )}
      {/*{addMaterials && (*/}
      {/*  <InventoryItemsDetailsOperationsMaterialsButton index={index} onClose={() => setAddMaterials(false)} />*/}
      {/*)}*/}
    </>
  );
};

export default InventoryItemsDetailsOperationsTableRow;
