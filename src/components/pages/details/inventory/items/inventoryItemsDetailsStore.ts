import {atom} from 'jotai';
import {atomFamily} from 'jotai/utils';

export type InventoryItemTab = 'files' | 'history' | 'materials' | 'operations' | 'variants';

export const inventoryItemTabAtom = atomFamily((_key: string) => atom<InventoryItemTab>('history'));

export const inventoryItemMaterialsSearchQueryAtom = atomFamily((_key: string) => atom<string>(''));

// Atom to track which operations are expanded (open) for each item
// Key format: `${itemId}-${operationId}`
export const inventoryItemExpandedOperationsAtom = atomFamily((_key: string) => atom<Set<string>>(new Set()));
